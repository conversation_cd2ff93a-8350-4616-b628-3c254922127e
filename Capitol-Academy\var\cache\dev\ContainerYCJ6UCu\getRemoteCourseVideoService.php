<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getRemoteCourseVideoService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.bzxg59p.App\Entity\RemoteCourseVideo' shared service.
     *
     * @return \App\Entity\RemoteCourseVideo
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.bzxg59p": it needs an instance of "App\\Entity\\RemoteCourseVideo" but this type has been excluded in "config/services.yaml".');
    }
}
