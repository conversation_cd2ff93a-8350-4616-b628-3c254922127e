<?php

namespace App\Form;

use App\Entity\OnsiteCourse;
use App\Repository\CategoryRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class OnsiteCourseType extends AbstractType
{
    private CategoryRepository $categoryRepository;

    public function __construct(CategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('code', TextType::class, [
                'label' => 'Course Code',
                'attr' => [
                    'class' => 'form-control enhanced-field',
                    'placeholder' => 'e.g., TRAD101, FIN200',
                    'maxlength' => 10,
                    'pattern' => '[A-Za-z]{2,4}[0-9]{1,4}',
                    'title' => 'Format: 2-4 letters followed by 1-4 numbers (e.g., TRAD101, FIN200)'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Course code is required']),
                    new Length(['max' => 10, 'maxMessage' => 'Course code cannot be longer than 10 characters'])
                ]
            ])
            ->add('title', TextType::class, [
                'label' => 'Course Title',
                'attr' => [
                    'class' => 'form-control enhanced-field',
                    'placeholder' => 'Enter comprehensive course title',
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Title is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Title cannot be longer than 255 characters'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Course Description',
                'required' => true,
                'attr' => [
                    'class' => 'form-control enhanced-field',
                    'rows' => 8,
                    'placeholder' => 'Enter comprehensive course description including objectives, target audience, and key topics...'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Description is required']),
                    new Length(['max' => 2000, 'maxMessage' => 'Description cannot be longer than 2000 characters'])
                ]
            ])
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => $this->getCategoryChoices(),
                'attr' => [
                    'class' => 'form-select enhanced-dropdown'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Category is required'])
                ]
            ])
            ->add('level', ChoiceType::class, [
                'label' => 'Level',
                'choices' => [
                    'Choose a level...' => '',
                    'Beginner' => 'Beginner',
                    'Intermediate' => 'Intermediate',
                    'Advanced' => 'Advanced'
                ],
                'required' => true,
                'attr' => [
                    'class' => 'form-select enhanced-dropdown'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Level is required'])
                ]
            ])
            ->add('duration', IntegerType::class, [
                'label' => 'Duration (Minutes)',
                'required' => true,
                'attr' => [
                    'class' => 'form-control enhanced-field',
                    'placeholder' => 'e.g., 120',
                    'min' => 1,
                    'max' => 10000
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Duration is required']),
                    new GreaterThanOrEqual(['value' => 1, 'message' => 'Duration must be at least 1 minute'])
                ]
            ])
            ->add('price', MoneyType::class, [
                'label' => 'Price (USD)',
                'currency' => 'USD',
                'attr' => [
                    'class' => 'form-control enhanced-field',
                    'placeholder' => '0.00',
                    'step' => '0.01',
                    'min' => '0'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Price is required']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Price must be greater than or equal to 0'])
                ]
            ])
            ->add('thumbnailFile', FileType::class, [
                'label' => 'Thumbnail Image',
                'mapped' => false,
                'required' => true,
                'attr' => [
                    'class' => 'form-control enhanced-file-field',
                    'accept' => 'image/jpeg,image/png,image/jpg'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Thumbnail image is required']),
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/jpg'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, JPG)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 5MB.'
                    ])
                ],
                'help' => 'Upload a thumbnail image (300x200px recommended). Max size: 5MB'
            ])
            ->add('bannerFile', FileType::class, [
                'label' => 'Banner Image',
                'mapped' => false,
                'required' => true,
                'attr' => [
                    'class' => 'form-control enhanced-file-field',
                    'accept' => 'image/jpeg,image/png,image/jpg'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Banner image is required']),
                    new File([
                        'maxSize' => '10M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/jpg'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, JPG)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 10MB.'
                    ])
                ],
                'help' => 'Upload a banner image (1200x400px recommended). Max size: 10MB'
            ])
            ->add('hasModules', CheckboxType::class, [
                'label' => 'Enable Course Modules',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Check this to enable modular structure for this onsite course'
            ])
            ->add('is_active', CheckboxType::class, [
                'label' => 'Active',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Check this to make the onsite course visible to students'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => OnsiteCourse::class,
        ]);
    }

    private function getCategoryChoices(): array
    {
        $categories = $this->categoryRepository->findForCourses();
        $choices = ['Choose a category...' => ''];
        
        foreach ($categories as $category) {
            $choices[$category->getName()] = $category->getName();
        }
        
        return $choices;
    }
}
