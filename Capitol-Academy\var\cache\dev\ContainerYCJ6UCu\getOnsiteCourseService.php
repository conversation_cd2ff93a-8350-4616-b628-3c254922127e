<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getOnsiteCourseService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.B22XE3C.App\Entity\OnsiteCourse' shared service.
     *
     * @return \App\Entity\OnsiteCourse
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.B22XE3C": it needs an instance of "App\\Entity\\OnsiteCourse" but this type has been excluded in "config/services.yaml".');
    }
}
