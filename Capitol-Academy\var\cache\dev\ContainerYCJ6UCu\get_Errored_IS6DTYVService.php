<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_Errored_IS6DTYVService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored.IS6DTYV' shared service.
     *
     * @return \App\Controller\CourseModuleRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot determine controller argument for "App\\Controller\\CourseController::showModule()": the $moduleRepository argument is type-hinted with the non-existent class or interface: "App\\Controller\\CourseModuleRepository". Did you forget to add a use statement?');
    }
}
