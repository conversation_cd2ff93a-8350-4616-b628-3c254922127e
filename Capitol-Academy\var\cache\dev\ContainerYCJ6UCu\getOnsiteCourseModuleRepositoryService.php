<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getOnsiteCourseModuleRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\OnsiteCourseModuleRepository' shared autowired service.
     *
     * @return \App\Repository\OnsiteCourseModuleRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'OnsiteCourseModuleRepository.php';

        return $container->privates['App\\Repository\\OnsiteCourseModuleRepository'] = new \App\Repository\OnsiteCourseModuleRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
