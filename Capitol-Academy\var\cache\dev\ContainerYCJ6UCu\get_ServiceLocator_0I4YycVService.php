<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_0I4YycVService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.0I4YycV' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.0I4YycV'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'chapter' => ['privates', '.errored..service_locator.0I4YycV.App\\Entity\\RemoteCourseChapter', NULL, 'Cannot autowire service ".service_locator.0I4YycV": it needs an instance of "App\\Entity\\RemoteCourseChapter" but this type has been excluded in "config/services.yaml".'],
        ], [
            'chapter' => 'App\\Entity\\RemoteCourseChapter',
        ]);
    }
}
