<?php

/**
 * This file has been auto-generated
 * by the Symfony Routing Component.
 */

return [
    false, // $matchHost
    [ // $staticRoutes
        '/_profiler' => [[['_route' => '_profiler_home', '_controller' => 'web_profiler.controller.profiler::homeAction'], null, null, null, true, false, null]],
        '/_profiler/search' => [[['_route' => '_profiler_search', '_controller' => 'web_profiler.controller.profiler::searchAction'], null, null, null, false, false, null]],
        '/_profiler/search_bar' => [[['_route' => '_profiler_search_bar', '_controller' => 'web_profiler.controller.profiler::searchBarAction'], null, null, null, false, false, null]],
        '/_profiler/phpinfo' => [[['_route' => '_profiler_phpinfo', '_controller' => 'web_profiler.controller.profiler::phpinfoAction'], null, null, null, false, false, null]],
        '/_profiler/xdebug' => [[['_route' => '_profiler_xdebug', '_controller' => 'web_profiler.controller.profiler::xdebugAction'], null, null, null, false, false, null]],
        '/_profiler/open' => [[['_route' => '_profiler_open_file', '_controller' => 'web_profiler.controller.profiler::openAction'], null, null, null, false, false, null]],
        '/admin/categories' => [[['_route' => 'admin_category_index', '_controller' => 'App\\Controller\\Admin\\CategoryController::index'], null, ['GET' => 0], null, false, false, null]],
        '/admin/categories/new' => [[['_route' => 'admin_category_new', '_controller' => 'App\\Controller\\Admin\\CategoryController::new'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/dashboard' => [
            [['_route' => 'admin_dashboard_alt', '_controller' => 'App\\Controller\\Admin\\DashboardController::index'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'admin_dashboard', '_controller' => 'App\\Controller\\AdminController::dashboard'], null, null, null, false, false, null],
        ],
        '/admin/orders' => [
            [['_route' => 'admin_order_index', '_controller' => 'App\\Controller\\Admin\\OrderController::index'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'admin_orders', '_controller' => 'App\\Controller\\AdminController::orders'], null, null, null, false, false, null],
        ],
        '/admin/videos' => [
            [['_route' => 'admin_video_index', '_controller' => 'App\\Controller\\Admin\\VideoController::index'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'admin_videos', '_controller' => 'App\\Controller\\AdminController::videos'], null, null, null, false, false, null],
        ],
        '/admin/videos/new' => [[['_route' => 'admin_video_new', '_controller' => 'App\\Controller\\Admin\\VideoController::new'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/users' => [[['_route' => 'admin_users', '_controller' => 'App\\Controller\\AdminController::users'], null, null, null, false, false, null]],
        '/admin/courses' => [[['_route' => 'admin_courses', '_controller' => 'App\\Controller\\AdminController::coursesRedirect'], null, null, null, false, false, null]],
        '/admin/onsite-courses' => [[['_route' => 'admin_onsite_courses', '_controller' => 'App\\Controller\\AdminController::onsiteCourses'], null, null, null, false, false, null]],
        '/admin/onsite-courses/create' => [[['_route' => 'admin_onsite_course_create', '_controller' => 'App\\Controller\\AdminController::createOnsiteCourse'], null, null, null, false, false, null]],
        '/admin/contacts' => [[['_route' => 'admin_contacts', '_controller' => 'App\\Controller\\AdminController::contacts'], null, null, null, false, false, null]],
        '/admin/test-email' => [[['_route' => 'admin_test_email', '_controller' => 'App\\Controller\\AdminController::testEmail'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/add-admin' => [[['_route' => 'admin_add_admin', '_controller' => 'App\\Controller\\AdminController::addAdmin'], null, null, null, false, false, null]],
        '/admin/profile' => [[['_route' => 'admin_profile', '_controller' => 'App\\Controller\\AdminController::profile'], null, null, null, false, false, null]],
        '/admin/plans' => [[['_route' => 'admin_plans', '_controller' => 'App\\Controller\\AdminController::plans'], null, null, null, false, false, null]],
        '/admin/plans/create' => [[['_route' => 'admin_plan_create', '_controller' => 'App\\Controller\\AdminController::createPlan'], null, null, null, false, false, null]],
        '/admin/admins' => [[['_route' => 'admin_admins', '_controller' => 'App\\Controller\\AdminController::listAdmins'], null, null, null, false, false, null]],
        '/admin/promotional-banners' => [[['_route' => 'admin_promotional_banners', '_controller' => 'App\\Controller\\AdminController::promotionalBanners'], null, null, null, false, false, null]],
        '/admin/promotional-banners/create' => [[['_route' => 'admin_promotional_banner_create', '_controller' => 'App\\Controller\\AdminController::createPromotionalBanner'], null, null, null, false, false, null]],
        '/admin/partners' => [[['_route' => 'admin_partners', '_controller' => 'App\\Controller\\AdminController::partnersManagement'], null, null, null, false, false, null]],
        '/admin/partners/create' => [[['_route' => 'admin_partners_create', '_controller' => 'App\\Controller\\AdminController::createPartner'], null, null, null, false, false, null]],
        '/admin/instructors' => [[['_route' => 'admin_instructor_index', '_controller' => 'App\\Controller\\AdminInstructorController::index'], null, ['GET' => 0], null, true, false, null]],
        '/admin/instructors/new' => [[['_route' => 'admin_instructor_new', '_controller' => 'App\\Controller\\AdminInstructorController::new'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/market_analysis' => [[['_route' => 'admin_market_analysis_index', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::index'], null, ['GET' => 0], null, true, false, null]],
        '/admin/market_analysis/create' => [[['_route' => 'admin_market_analysis_create', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::create'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/remote-courses' => [[['_route' => 'admin_remote_course_index', '_controller' => 'App\\Controller\\AdminRemoteCourseController::index'], null, ['GET' => 0], null, false, false, null]],
        '/admin/remote-courses/create' => [[['_route' => 'admin_remote_course_create', '_controller' => 'App\\Controller\\AdminRemoteCourseController::create'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/login' => [[['_route' => 'admin_login', '_controller' => 'App\\Controller\\AdminSecurityController::login'], null, null, null, false, false, null]],
        '/admin/login_check' => [[['_route' => 'admin_login_check', '_controller' => 'App\\Controller\\AdminSecurityController::loginCheck'], null, null, null, false, false, null]],
        '/admin/logout' => [[['_route' => 'admin_logout', '_controller' => 'App\\Controller\\AdminSecurityController::logout'], null, null, null, false, false, null]],
        '/admin/create-admin' => [[['_route' => 'admin_create_admin', '_controller' => 'App\\Controller\\AdminSecurityController::createAdmin'], null, null, null, false, false, null]],
        '/cart' => [[['_route' => 'app_cart', '_controller' => 'App\\Controller\\CartController::index'], null, ['GET' => 0], null, false, false, null]],
        '/cart/add' => [[['_route' => 'app_cart_add', '_controller' => 'App\\Controller\\CartController::add'], null, ['POST' => 0], null, false, false, null]],
        '/cart/remove' => [[['_route' => 'app_cart_remove', '_controller' => 'App\\Controller\\CartController::remove'], null, ['POST' => 0], null, false, false, null]],
        '/cart/update' => [[['_route' => 'app_cart_update', '_controller' => 'App\\Controller\\CartController::update'], null, ['POST' => 0], null, false, false, null]],
        '/cart/clear' => [[['_route' => 'app_cart_clear', '_controller' => 'App\\Controller\\CartController::clear'], null, ['POST' => 0], null, false, false, null]],
        '/cart/count' => [[['_route' => 'app_cart_count', '_controller' => 'App\\Controller\\CartController::count'], null, ['GET' => 0], null, false, false, null]],
        '/cart/summary' => [[['_route' => 'app_cart_summary', '_controller' => 'App\\Controller\\CartController::summary'], null, ['GET' => 0], null, false, false, null]],
        '/cart/widget' => [[['_route' => 'app_cart_widget', '_controller' => 'App\\Controller\\CartController::widget'], null, ['GET' => 0], null, false, false, null]],
        '/cart/validate' => [[['_route' => 'app_cart_validate', '_controller' => 'App\\Controller\\CartController::validate'], null, ['POST' => 0], null, false, false, null]],
        '/checkout' => [[['_route' => 'app_checkout', '_controller' => 'App\\Controller\\CheckoutController::index'], null, ['GET' => 0], null, false, false, null]],
        '/checkout/create-order' => [[['_route' => 'app_checkout_create_order', '_controller' => 'App\\Controller\\CheckoutController::createOrder'], null, ['POST' => 0], null, false, false, null]],
        '/checkout/capture-order' => [[['_route' => 'app_checkout_capture_order', '_controller' => 'App\\Controller\\CheckoutController::captureOrder'], null, ['POST' => 0], null, false, false, null]],
        '/checkout/cancel' => [[['_route' => 'app_checkout_cancel', '_controller' => 'App\\Controller\\CheckoutController::cancel'], null, ['GET' => 0], null, false, false, null]],
        '/checkout/webhook/paypal' => [[['_route' => 'app_checkout_paypal_webhook', '_controller' => 'App\\Controller\\CheckoutController::paypalWebhook'], null, ['POST' => 0], null, false, false, null]],
        '/contact' => [[['_route' => 'app_contact', '_controller' => 'App\\Controller\\ContactController::index'], null, null, null, false, false, null]],
        '/contact/registration' => [[['_route' => 'app_contact_registration', '_controller' => 'App\\Controller\\ContactController::registration'], null, null, null, false, false, null]],
        '/contact/instructor' => [[['_route' => 'app_contact_instructor', '_controller' => 'App\\Controller\\ContactController::instructor'], null, null, null, false, false, null]],
        '/contact/webinar' => [[['_route' => 'app_contact_webinar', '_controller' => 'App\\Controller\\ContactController::webinar'], null, null, null, false, false, null]],
        '/message' => [[['_route' => 'app_message', '_controller' => 'App\\Controller\\ContactController::message'], null, null, null, false, false, null]],
        '/contact/unified' => [[['_route' => 'app_contact_unified', '_controller' => 'App\\Controller\\ContactController::unifiedContact'], null, ['POST' => 0], null, false, false, null]],
        '/courses' => [
            [['_route' => 'app_courses_list', '_controller' => 'App\\Controller\\CourseController::list'], null, null, null, true, false, null],
            [['_route' => 'app_courses', '_controller' => 'App\\Controller\\CourseController::list'], null, null, null, true, false, null],
            [['_route' => 'public_courses_index', '_controller' => 'App\\Controller\\PublicCourseController::index'], null, null, null, true, false, null],
        ],
        '/connect/google' => [[['_route' => 'connect_google_start', '_controller' => 'App\\Controller\\GoogleAuthController::connectAction'], null, null, null, false, false, null]],
        '/connect/google/check' => [[['_route' => 'connect_google_check', '_controller' => 'App\\Controller\\GoogleAuthController::connectCheckAction'], null, null, null, false, false, null]],
        '/' => [[['_route' => 'app_home', '_controller' => 'App\\Controller\\HomeController::index'], null, null, null, false, false, null]],
        '/about' => [[['_route' => 'app_about', '_controller' => 'App\\Controller\\HomeController::about'], null, null, null, false, false, null]],
        '/partnership' => [[['_route' => 'app_partnership', '_controller' => 'App\\Controller\\HomeController::partnership'], null, null, null, false, false, null]],
        '/diplomas' => [[['_route' => 'app_diplomas', '_controller' => 'App\\Controller\\HomeController::diplomas'], null, null, null, false, false, null]],
        '/instructors' => [[['_route' => 'app_instructors', '_controller' => 'App\\Controller\\HomeController::instructors'], null, null, null, false, false, null]],
        '/executive-program' => [[['_route' => 'app_executive_program', '_controller' => 'App\\Controller\\HomeController::executiveProgram'], null, null, null, false, false, null]],
        '/trading-tools/market-analysis' => [[['_route' => 'app_market_analysis', '_controller' => 'App\\Controller\\MarketAnalysisController::index'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/filter' => [[['_route' => 'api_market_analysis_filter', '_controller' => 'App\\Controller\\MarketAnalysisController::filter'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/featured' => [[['_route' => 'api_market_analysis_featured', '_controller' => 'App\\Controller\\MarketAnalysisController::getFeatured'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/recent' => [[['_route' => 'api_market_analysis_recent', '_controller' => 'App\\Controller\\MarketAnalysisController::getRecent'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/stats' => [[['_route' => 'api_market_analysis_stats', '_controller' => 'App\\Controller\\MarketAnalysisController::getStats'], null, ['GET' => 0], null, false, false, null]],
        '/onsite-courses' => [[['_route' => 'app_onsite_course_index', '_controller' => 'App\\Controller\\OnsiteCourseController::index'], null, null, null, false, false, null]],
        '/forgot-password' => [[['_route' => 'app_forgot_password', '_controller' => 'App\\Controller\\PasswordResetController::forgotPassword'], null, null, null, false, false, null]],
        '/reset-password' => [[['_route' => 'app_reset_password', '_controller' => 'App\\Controller\\PasswordResetController::resetPassword'], null, null, null, false, false, null]],
        '/verify-reset-code' => [[['_route' => 'app_verify_reset_code', '_controller' => 'App\\Controller\\PasswordResetController::verifyResetCode'], null, null, null, false, false, null]],
        '/reset-password-merged' => [[['_route' => 'app_reset_password_merged', '_controller' => 'App\\Controller\\PasswordResetController::resetPasswordMerged'], null, null, null, false, false, null]],
        '/payment/success' => [[['_route' => 'payment_success', '_controller' => 'App\\Controller\\PaymentController::paymentSuccess'], null, null, null, false, false, null]],
        '/payment/cancel' => [[['_route' => 'payment_cancel', '_controller' => 'App\\Controller\\PaymentController::paymentCancel'], null, null, null, false, false, null]],
        '/stripe/webhook' => [[['_route' => 'stripe_webhook', '_controller' => 'App\\Controller\\PaymentController::stripeWebhook'], null, ['POST' => 0], null, false, false, null]],
        '/search' => [[['_route' => 'app_search', '_controller' => 'App\\Controller\\SearchController::search'], null, ['GET' => 0], null, false, false, null]],
        '/api/search/autocomplete' => [[['_route' => 'api_search_autocomplete', '_controller' => 'App\\Controller\\SearchController::autocomplete'], null, ['GET' => 0], null, false, false, null]],
        '/login' => [[['_route' => 'app_login', '_controller' => 'App\\Controller\\SecurityController::login'], null, null, null, false, false, null]],
        '/register' => [[['_route' => 'app_register', '_controller' => 'App\\Controller\\SecurityController::register'], null, null, null, false, false, null]],
        '/logout' => [[['_route' => 'app_logout', '_controller' => 'App\\Controller\\SecurityController::logout'], null, null, null, false, false, null]],
        '/user/home' => [[['_route' => 'app_user_home', '_controller' => 'App\\Controller\\UserController::home'], null, null, null, false, false, null]],
        '/user/profile' => [[['_route' => 'app_user_profile', '_controller' => 'App\\Controller\\UserController::profile'], null, null, null, false, false, null]],
        '/user/dashboard' => [[['_route' => 'app_user_dashboard', '_controller' => 'App\\Controller\\UserController::dashboard'], null, null, null, false, false, null]],
        '/user/orders' => [[['_route' => 'app_user_orders', '_controller' => 'App\\Controller\\UserOrderController::index'], null, ['GET' => 0], null, false, false, null]],
        '/videos' => [
            [['_route' => 'app_videos_list', '_controller' => 'App\\Controller\\VideoController::list'], null, null, null, true, false, null],
            [['_route' => 'app_videos', '_controller' => 'App\\Controller\\VideoController::list'], null, null, null, true, false, null],
        ],
    ],
    [ // $regexpList
        0 => '{^(?'
                .'|/_(?'
                    .'|error/(\\d+)(?:\\.([^/]++))?(*:38)'
                    .'|wdt/([^/]++)(*:57)'
                    .'|profiler/(?'
                        .'|font/([^/\\.]++)\\.woff2(*:98)'
                        .'|([^/]++)(?'
                            .'|/(?'
                                .'|search/results(*:134)'
                                .'|router(*:148)'
                                .'|exception(?'
                                    .'|(*:168)'
                                    .'|\\.css(*:181)'
                                .')'
                            .')'
                            .'|(*:191)'
                        .')'
                    .')'
                .')'
                .'|/admin/(?'
                    .'|c(?'
                        .'|ategories/([^/]++)(?'
                            .'|(*:237)'
                            .'|/toggle\\-(?'
                                .'|status(*:263)'
                                .'|courses(*:278)'
                                .'|videos(*:292)'
                            .')'
                        .')'
                        .'|ontacts/([^/]++)(?'
                            .'|(*:321)'
                            .'|/(?'
                                .'|preview(*:340)'
                                .'|toggle\\-processed(*:365)'
                                .'|delete(*:379)'
                            .')'
                        .')'
                    .')'
                    .'|o(?'
                        .'|rders/(?'
                            .'|([^/]++)(?'
                                .'|(*:414)'
                                .'|/re(?'
                                    .'|fund(*:432)'
                                    .'|send\\-access(*:452)'
                                .')'
                            .')'
                            .'|export(*:468)'
                        .')'
                        .'|nsite\\-courses/(?'
                            .'|([^/]++)/edit(*:508)'
                            .'|(\\d+)/delete(*:528)'
                            .'|(\\d+)/toggle\\-status(*:556)'
                            .'|([^/]++)/preview(*:580)'
                        .')'
                    .')'
                    .'|videos/([^/]++)(?'
                        .'|(*:608)'
                        .'|/(?'
                            .'|edit(*:624)'
                            .'|delete(*:638)'
                            .'|toggle\\-status(*:660)'
                            .'|remote\\-course\\-usage(*:689)'
                        .')'
                    .')'
                    .'|users/(?'
                        .'|(\\d+)(*:713)'
                        .'|([a-zA-Z0-9\\-_\\.]+)(*:740)'
                        .'|(\\d+)/block(*:759)'
                        .'|(\\d+)/unblock(*:780)'
                        .'|(\\d+)/toggle\\-status(*:808)'
                        .'|(\\d+)/delete(*:828)'
                    .')'
                    .'|admin/(?'
                        .'|([^/]++)/(?'
                            .'|toggle\\-status(*:872)'
                            .'|delete(*:886)'
                        .')'
                        .'|(\\d+)/view(*:905)'
                        .'|(\\d+)/edit(*:923)'
                    .')'
                    .'|p(?'
                        .'|lans/([^/]++)/(?'
                            .'|edit(*:957)'
                            .'|preview(*:972)'
                            .'|toggle\\-status(*:994)'
                        .')'
                        .'|romotional\\-banners/(?'
                            .'|(\\d+)/edit(*:1036)'
                            .'|(\\d+)/delete(*:1057)'
                            .'|(\\d+)/toggle\\-status(*:1086)'
                        .')'
                        .'|artners/(?'
                            .'|(\\d+)(*:1112)'
                            .'|([a-z0-9\\-]+)(*:1134)'
                            .'|(\\d+)/edit(*:1153)'
                            .'|(\\d+)/delete(*:1174)'
                            .'|(\\d+)/toggle(*:1195)'
                            .'|reorder(*:1211)'
                        .')'
                    .')'
                    .'|instructors/(?'
                        .'|([^/]++)(?'
                            .'|(*:1248)'
                            .'|/(?'
                                .'|edit(*:1265)'
                                .'|delete(*:1280)'
                                .'|toggle\\-status(*:1303)'
                            .')'
                        .')'
                        .'|reorder(*:1321)'
                        .'|([^/]++)/print(*:1344)'
                    .')'
                    .'|market_analysis/(?'
                        .'|([^/]++)(?'
                            .'|/edit(?'
                                .'|(*:1392)'
                                .'|(*:1401)'
                            .')'
                            .'|(*:1411)'
                        .')'
                        .'|(\\d+)(*:1426)'
                        .'|([^/]++)/(?'
                            .'|delete(*:1453)'
                            .'|toggle\\-status(*:1476)'
                        .')'
                    .')'
                    .'|remote\\-courses/(?'
                        .'|([^/]++)/(?'
                            .'|edit(*:1522)'
                            .'|chapters(?'
                                .'|(*:1542)'
                                .'|/create(*:1558)'
                            .')'
                        .')'
                        .'|chapters/([^/]++)/(?'
                            .'|edit(*:1594)'
                            .'|videos(*:1609)'
                        .')'
                        .'|([^/]++)/(?'
                            .'|preview(*:1638)'
                            .'|toggle\\-status(*:1661)'
                            .'|delete(*:1676)'
                        .')'
                        .'|chapter(?'
                            .'|s/([^/]++)/add\\-video(*:1717)'
                            .'|\\-videos/([^/]++)/remove(*:1750)'
                        .')'
                    .')'
                .')'
                .'|/c(?'
                    .'|heckout/success/([^/]++)(*:1791)'
                    .'|ourse(?'
                        .'|s/(?'
                            .'|([^/]++)(?'
                                .'|(*:1824)'
                                .'|/([^/]++)(*:1842)'
                            .')'
                            .'|f(?'
                                .'|inancial\\-markets(*:1873)'
                                .'|undamental\\-analysis(*:1902)'
                            .')'
                            .'|t(?'
                                .'|echnical\\-analysis(*:1934)'
                                .'|rading\\-strategies(*:1961)'
                            .')'
                            .'|p(?'
                                .'|sychological\\-analysis(*:1997)'
                                .'|rofessional\\-trader(*:2025)'
                            .')'
                            .'|capital\\-management(*:2054)'
                            .'|risk\\-management(*:2079)'
                            .'|day\\-trading(*:2100)'
                            .'|add\\-to\\-cart/([^/]++)(*:2131)'
                            .'|([^/]++)(?'
                                .'|(*:2151)'
                                .'|/contact(*:2168)'
                            .')'
                        .')'
                        .'|/([^/]++)/contact(*:2196)'
                    .')'
                .')'
                .'|/trading\\-tools/market\\-analysis/([^/]++)(*:2248)'
                .'|/onsite\\-courses/(?'
                    .'|([^/]++)(*:2285)'
                    .'|c(?'
                        .'|ategory/([^/]++)(*:2314)'
                        .'|ryptocurrency\\-trading(*:2345)'
                    .')'
                    .'|level/([^/]++)(*:2369)'
                    .'|f(?'
                        .'|inancial\\-markets(*:2399)'
                        .'|orex\\-trading(*:2421)'
                    .')'
                    .'|t(?'
                        .'|echnical\\-analysis(*:2453)'
                        .'|rading\\-strategies(*:2480)'
                    .')'
                    .'|risk\\-management(*:2506)'
                    .'|p(?'
                        .'|sychology\\-trading(*:2537)'
                        .'|ortfolio\\-management(*:2566)'
                    .')'
                    .'|algorithmic\\-trading(*:2596)'
                    .'|options\\-trading(*:2621)'
                .')'
                .'|/user/orders/([^/]++)(*:2652)'
                .'|/videos/(?'
                    .'|([^/]++)(?'
                        .'|(*:2683)'
                        .'|/vdocipher\\-token(*:2709)'
                    .')'
                    .'|purchase(*:2727)'
                    .'|([^/]++)/track\\-(?'
                        .'|view(*:2759)'
                        .'|engagement(*:2778)'
                    .')'
                .')'
            .')/?$}sDu',
    ],
    [ // $dynamicRoutes
        38 => [[['_route' => '_preview_error', '_controller' => 'error_controller::preview', '_format' => 'html'], ['code', '_format'], null, null, false, true, null]],
        57 => [[['_route' => '_wdt', '_controller' => 'web_profiler.controller.profiler::toolbarAction'], ['token'], null, null, false, true, null]],
        98 => [[['_route' => '_profiler_font', '_controller' => 'web_profiler.controller.profiler::fontAction'], ['fontName'], null, null, false, false, null]],
        134 => [[['_route' => '_profiler_search_results', '_controller' => 'web_profiler.controller.profiler::searchResultsAction'], ['token'], null, null, false, false, null]],
        148 => [[['_route' => '_profiler_router', '_controller' => 'web_profiler.controller.router::panelAction'], ['token'], null, null, false, false, null]],
        168 => [[['_route' => '_profiler_exception', '_controller' => 'web_profiler.controller.exception_panel::body'], ['token'], null, null, false, false, null]],
        181 => [[['_route' => '_profiler_exception_css', '_controller' => 'web_profiler.controller.exception_panel::stylesheet'], ['token'], null, null, false, false, null]],
        191 => [[['_route' => '_profiler', '_controller' => 'web_profiler.controller.profiler::panelAction'], ['token'], null, null, false, true, null]],
        237 => [[['_route' => 'admin_category_show', '_controller' => 'App\\Controller\\Admin\\CategoryController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        263 => [[['_route' => 'admin_category_toggle_status', '_controller' => 'App\\Controller\\Admin\\CategoryController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        278 => [[['_route' => 'admin_category_toggle_courses', '_controller' => 'App\\Controller\\Admin\\CategoryController::toggleCourses'], ['id'], ['POST' => 0], null, false, false, null]],
        292 => [[['_route' => 'admin_category_toggle_videos', '_controller' => 'App\\Controller\\Admin\\CategoryController::toggleVideos'], ['id'], ['POST' => 0], null, false, false, null]],
        321 => [[['_route' => 'admin_contact_show', '_controller' => 'App\\Controller\\AdminController::contactShow'], ['slug'], null, null, false, true, null]],
        340 => [[['_route' => 'admin_contact_preview', '_controller' => 'App\\Controller\\AdminController::contactPreview'], ['slug'], null, null, false, false, null]],
        365 => [[['_route' => 'admin_contact_toggle_processed', '_controller' => 'App\\Controller\\AdminController::toggleContactProcessed'], ['slug'], ['POST' => 0], null, false, false, null]],
        379 => [[['_route' => 'admin_contact_delete', '_controller' => 'App\\Controller\\AdminController::deleteContact'], ['slug'], ['POST' => 0], null, false, false, null]],
        414 => [[['_route' => 'admin_order_show', '_controller' => 'App\\Controller\\Admin\\OrderController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        432 => [[['_route' => 'admin_order_refund', '_controller' => 'App\\Controller\\Admin\\OrderController::refund'], ['id'], ['POST' => 0], null, false, false, null]],
        452 => [[['_route' => 'admin_order_resend_access', '_controller' => 'App\\Controller\\Admin\\OrderController::resendAccess'], ['id'], ['POST' => 0], null, false, false, null]],
        468 => [[['_route' => 'admin_order_export', '_controller' => 'App\\Controller\\Admin\\OrderController::export'], [], ['GET' => 0], null, false, false, null]],
        508 => [[['_route' => 'admin_onsite_course_edit', '_controller' => 'App\\Controller\\AdminController::editOnsiteCourse'], ['code'], null, null, false, false, null]],
        528 => [[['_route' => 'admin_onsite_course_delete', '_controller' => 'App\\Controller\\AdminController::deleteOnsiteCourse'], ['id'], ['POST' => 0], null, false, false, null]],
        556 => [[['_route' => 'admin_onsite_course_toggle_status', '_controller' => 'App\\Controller\\AdminController::toggleOnsiteCourseStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        580 => [[['_route' => 'admin_onsite_course_preview', '_controller' => 'App\\Controller\\AdminController::previewOnsiteCourse'], ['code'], null, null, false, false, null]],
        608 => [[['_route' => 'admin_video_show', '_controller' => 'App\\Controller\\Admin\\VideoController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        624 => [[['_route' => 'admin_video_edit', '_controller' => 'App\\Controller\\Admin\\VideoController::edit'], ['id'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        638 => [[['_route' => 'admin_video_delete', '_controller' => 'App\\Controller\\Admin\\VideoController::delete'], ['id'], ['POST' => 0], null, false, false, null]],
        660 => [[['_route' => 'admin_video_toggle_status', '_controller' => 'App\\Controller\\Admin\\VideoController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        689 => [[['_route' => 'admin_video_remote_course_usage', '_controller' => 'App\\Controller\\Admin\\VideoController::remoteCourseUsage'], ['id'], ['GET' => 0], null, false, false, null]],
        713 => [[['_route' => 'admin_user_show', '_controller' => 'App\\Controller\\AdminController::userShow'], ['id'], null, null, false, true, null]],
        740 => [[['_route' => 'admin_user_details', '_controller' => 'App\\Controller\\AdminController::userDetails'], ['emailPrefix'], null, null, false, true, null]],
        759 => [[['_route' => 'admin_user_block', '_controller' => 'App\\Controller\\AdminController::blockUser'], ['id'], ['POST' => 0], null, false, false, null]],
        780 => [[['_route' => 'admin_user_unblock', '_controller' => 'App\\Controller\\AdminController::unblockUser'], ['id'], ['POST' => 0], null, false, false, null]],
        808 => [[['_route' => 'admin_user_toggle_status', '_controller' => 'App\\Controller\\AdminController::toggleUserStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        828 => [[['_route' => 'admin_user_delete', '_controller' => 'App\\Controller\\AdminController::deleteUser'], ['id'], ['POST' => 0], null, false, false, null]],
        872 => [[['_route' => 'admin_toggle_status', '_controller' => 'App\\Controller\\AdminController::toggleAdminStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        886 => [[['_route' => 'admin_delete', '_controller' => 'App\\Controller\\AdminController::deleteAdmin'], ['id'], ['POST' => 0], null, false, false, null]],
        905 => [[['_route' => 'admin_admin_view', '_controller' => 'App\\Controller\\AdminController::viewAdmin'], ['id'], null, null, false, false, null]],
        923 => [[['_route' => 'admin_admin_edit', '_controller' => 'App\\Controller\\AdminController::editAdmin'], ['id'], null, null, false, false, null]],
        957 => [[['_route' => 'admin_plan_edit', '_controller' => 'App\\Controller\\AdminController::editPlan'], ['code'], null, null, false, false, null]],
        972 => [[['_route' => 'admin_plan_preview', '_controller' => 'App\\Controller\\AdminController::previewPlan'], ['code'], null, null, false, false, null]],
        994 => [[['_route' => 'admin_plan_toggle_status', '_controller' => 'App\\Controller\\AdminController::togglePlanStatus'], ['code'], ['POST' => 0], null, false, false, null]],
        1036 => [[['_route' => 'admin_promotional_banner_edit', '_controller' => 'App\\Controller\\AdminController::editPromotionalBanner'], ['id'], null, null, false, false, null]],
        1057 => [[['_route' => 'admin_promotional_banner_delete', '_controller' => 'App\\Controller\\AdminController::deletePromotionalBanner'], ['id'], ['POST' => 0], null, false, false, null]],
        1086 => [[['_route' => 'admin_promotional_banner_toggle_status', '_controller' => 'App\\Controller\\AdminController::togglePromotionalBannerStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        1112 => [[['_route' => 'admin_partners_show', '_controller' => 'App\\Controller\\AdminController::showPartner'], ['id'], null, null, false, true, null]],
        1134 => [[['_route' => 'admin_partners_show_by_slug', '_controller' => 'App\\Controller\\AdminController::showPartnerBySlug'], ['slug'], null, null, false, true, null]],
        1153 => [[['_route' => 'admin_partner_edit', '_controller' => 'App\\Controller\\AdminController::editPartner'], ['id'], null, null, false, false, null]],
        1174 => [[['_route' => 'admin_partners_delete', '_controller' => 'App\\Controller\\AdminController::deletePartner'], ['id'], ['POST' => 0], null, false, false, null]],
        1195 => [[['_route' => 'admin_partners_toggle', '_controller' => 'App\\Controller\\AdminController::togglePartner'], ['id'], ['POST' => 0], null, false, false, null]],
        1211 => [[['_route' => 'admin_partners_reorder', '_controller' => 'App\\Controller\\AdminController::reorderPartners'], [], ['POST' => 0], null, false, false, null]],
        1248 => [[['_route' => 'admin_instructor_show', '_controller' => 'App\\Controller\\AdminInstructorController::show'], ['emailPrefix'], ['GET' => 0], null, false, true, null]],
        1265 => [[['_route' => 'admin_instructor_edit', '_controller' => 'App\\Controller\\AdminInstructorController::edit'], ['emailPrefix'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1280 => [[['_route' => 'admin_instructor_delete', '_controller' => 'App\\Controller\\AdminInstructorController::delete'], ['id'], ['POST' => 0], null, false, false, null]],
        1303 => [[['_route' => 'admin_instructor_toggle_status', '_controller' => 'App\\Controller\\AdminInstructorController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        1321 => [[['_route' => 'admin_instructor_reorder', '_controller' => 'App\\Controller\\AdminInstructorController::reorder'], [], ['POST' => 0], null, false, false, null]],
        1344 => [[['_route' => 'admin_instructor_print', '_controller' => 'App\\Controller\\AdminInstructorController::print'], ['id'], ['GET' => 0], null, false, false, null]],
        1392 => [[['_route' => 'admin_market_analysis_edit_readable', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::editBySlug'], ['slug'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1401 => [[['_route' => 'admin_market_analysis_edit', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::edit'], ['id'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1411 => [[['_route' => 'admin_market_analysis_show_readable', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::showBySlug'], ['slug'], ['GET' => 0], null, false, true, null]],
        1426 => [[['_route' => 'admin_market_analysis_show', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        1453 => [[['_route' => 'admin_market_analysis_delete', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::delete'], ['id'], ['POST' => 0], null, false, false, null]],
        1476 => [[['_route' => 'admin_market_analysis_toggle_status', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        1522 => [[['_route' => 'admin_remote_course_edit', '_controller' => 'App\\Controller\\AdminRemoteCourseController::edit'], ['id'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1542 => [[['_route' => 'admin_remote_course_chapters', '_controller' => 'App\\Controller\\AdminRemoteCourseController::chapters'], ['id'], ['GET' => 0], null, false, false, null]],
        1558 => [[['_route' => 'admin_remote_course_chapter_create', '_controller' => 'App\\Controller\\AdminRemoteCourseController::createChapter'], ['id'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1594 => [[['_route' => 'admin_remote_course_chapter_edit', '_controller' => 'App\\Controller\\AdminRemoteCourseController::editChapter'], ['id'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1609 => [[['_route' => 'admin_remote_course_chapter_videos', '_controller' => 'App\\Controller\\AdminRemoteCourseController::chapterVideos'], ['id'], ['GET' => 0], null, false, false, null]],
        1638 => [[['_route' => 'admin_remote_course_preview', '_controller' => 'App\\Controller\\AdminRemoteCourseController::preview'], ['id'], ['GET' => 0], null, false, false, null]],
        1661 => [[['_route' => 'admin_remote_course_toggle_status', '_controller' => 'App\\Controller\\AdminRemoteCourseController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        1676 => [[['_route' => 'admin_remote_course_delete', '_controller' => 'App\\Controller\\AdminRemoteCourseController::delete'], ['id'], ['POST' => 0], null, false, false, null]],
        1717 => [[['_route' => 'admin_remote_course_add_video', '_controller' => 'App\\Controller\\AdminRemoteCourseController::addVideoToChapter'], ['id'], ['POST' => 0], null, false, false, null]],
        1750 => [[['_route' => 'admin_remote_course_remove_video', '_controller' => 'App\\Controller\\AdminRemoteCourseController::removeVideoFromChapter'], ['id'], ['POST' => 0], null, false, false, null]],
        1791 => [[['_route' => 'app_checkout_success', '_controller' => 'App\\Controller\\CheckoutController::success'], ['orderNumber'], ['GET' => 0], null, false, true, null]],
        1824 => [[['_route' => 'app_course_show', '_controller' => 'App\\Controller\\CourseController::show'], ['code'], null, null, false, true, null]],
        1842 => [[['_route' => 'app_course_module_show', '_controller' => 'App\\Controller\\CourseController::showModule'], ['courseCode', 'moduleCode'], null, null, false, true, null]],
        1873 => [[['_route' => 'app_course_fma', '_controller' => 'App\\Controller\\CourseController::financialMarkets'], [], null, null, false, false, null]],
        1902 => [[['_route' => 'app_course_fun', '_controller' => 'App\\Controller\\CourseController::fundamentalAnalysis'], [], null, null, false, false, null]],
        1934 => [[['_route' => 'app_course_tec', '_controller' => 'App\\Controller\\CourseController::technicalAnalysis'], [], null, null, false, false, null]],
        1961 => [[['_route' => 'app_course_trs', '_controller' => 'App\\Controller\\CourseController::tradingStrategies'], [], null, null, false, false, null]],
        1997 => [[['_route' => 'app_course_ssa', '_controller' => 'App\\Controller\\CourseController::psychologicalAnalysis'], [], null, null, false, false, null]],
        2025 => [[['_route' => 'app_course_pro', '_controller' => 'App\\Controller\\CourseController::professionalTrader'], [], null, null, false, false, null]],
        2054 => [[['_route' => 'app_course_mma', '_controller' => 'App\\Controller\\CourseController::capitalManagement'], [], null, null, false, false, null]],
        2079 => [[['_route' => 'app_course_rsk', '_controller' => 'App\\Controller\\CourseController::riskManagement'], [], null, null, false, false, null]],
        2100 => [[['_route' => 'app_course_dtr', '_controller' => 'App\\Controller\\CourseController::dayTrading'], [], null, null, false, false, null]],
        2131 => [[['_route' => 'app_course_add_to_cart', '_controller' => 'App\\Controller\\CourseController::addToCart'], ['code'], ['POST' => 0], null, false, true, null]],
        2151 => [[['_route' => 'public_course_detail', '_controller' => 'App\\Controller\\PublicCourseController::detail'], ['code'], null, null, false, true, null]],
        2168 => [[['_route' => 'public_course_contact', '_controller' => 'App\\Controller\\PublicCourseController::contact'], ['code'], ['POST' => 0], null, false, false, null]],
        2196 => [[['_route' => 'course_contact_redirect', '_controller' => 'App\\Controller\\PaymentController::redirectToContact'], ['code'], ['POST' => 0], null, false, false, null]],
        2248 => [[['_route' => 'app_market_analysis_show_seo', '_controller' => 'App\\Controller\\MarketAnalysisController::showBySeoSlug'], ['slug'], ['GET' => 0], null, false, true, null]],
        2285 => [[['_route' => 'app_onsite_course_show', '_controller' => 'App\\Controller\\OnsiteCourseController::show'], ['code'], null, null, false, true, null]],
        2314 => [[['_route' => 'app_onsite_course_category', '_controller' => 'App\\Controller\\OnsiteCourseController::category'], ['category'], null, null, false, true, null]],
        2345 => [[['_route' => 'app_onsite_course_ct', '_controller' => 'App\\Controller\\OnsiteCourseController::cryptocurrencyTrading'], [], null, null, false, false, null]],
        2369 => [[['_route' => 'app_onsite_course_level', '_controller' => 'App\\Controller\\OnsiteCourseController::level'], ['level'], null, null, false, true, null]],
        2399 => [[['_route' => 'app_onsite_course_fma', '_controller' => 'App\\Controller\\OnsiteCourseController::financialMarkets'], [], null, null, false, false, null]],
        2421 => [[['_route' => 'app_onsite_course_ft', '_controller' => 'App\\Controller\\OnsiteCourseController::forexTrading'], [], null, null, false, false, null]],
        2453 => [[['_route' => 'app_onsite_course_tec', '_controller' => 'App\\Controller\\OnsiteCourseController::technicalAnalysis'], [], null, null, false, false, null]],
        2480 => [[['_route' => 'app_onsite_course_trs', '_controller' => 'App\\Controller\\OnsiteCourseController::tradingStrategies'], [], null, null, false, false, null]],
        2506 => [[['_route' => 'app_onsite_course_rm', '_controller' => 'App\\Controller\\OnsiteCourseController::riskManagement'], [], null, null, false, false, null]],
        2537 => [[['_route' => 'app_onsite_course_pt', '_controller' => 'App\\Controller\\OnsiteCourseController::psychologyTrading'], [], null, null, false, false, null]],
        2566 => [[['_route' => 'app_onsite_course_pm', '_controller' => 'App\\Controller\\OnsiteCourseController::portfolioManagement'], [], null, null, false, false, null]],
        2596 => [[['_route' => 'app_onsite_course_at', '_controller' => 'App\\Controller\\OnsiteCourseController::algorithmicTrading'], [], null, null, false, false, null]],
        2621 => [[['_route' => 'app_onsite_course_ot', '_controller' => 'App\\Controller\\OnsiteCourseController::optionsTrading'], [], null, null, false, false, null]],
        2652 => [[['_route' => 'app_user_order_show', '_controller' => 'App\\Controller\\UserOrderController::show'], ['orderNumber'], ['GET' => 0], null, false, true, null]],
        2683 => [[['_route' => 'app_video_show', '_controller' => 'App\\Controller\\VideoController::show'], ['slug'], null, null, false, true, null]],
        2709 => [[['_route' => 'app_video_vdocipher_token', '_controller' => 'App\\Controller\\VideoController::getVdoCipherToken'], ['slug'], ['POST' => 0], null, false, false, null]],
        2727 => [[['_route' => 'app_video_purchase', '_controller' => 'App\\Controller\\VideoController::purchaseVideo'], [], ['POST' => 0], null, false, false, null]],
        2759 => [[['_route' => 'app_video_track_view', '_controller' => 'App\\Controller\\VideoController::trackVideoView'], ['slug'], ['POST' => 0], null, false, false, null]],
        2778 => [
            [['_route' => 'app_video_track_engagement', '_controller' => 'App\\Controller\\VideoController::trackVideoEngagement'], ['slug'], ['POST' => 0], null, false, false, null],
            [null, null, null, null, false, false, 0],
        ],
    ],
    null, // $checkCondition
];
