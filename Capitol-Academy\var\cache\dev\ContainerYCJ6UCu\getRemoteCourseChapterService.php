<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getRemoteCourseChapterService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.0I4YycV.App\Entity\RemoteCourseChapter' shared service.
     *
     * @return \App\Entity\RemoteCourseChapter
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.0I4YycV": it needs an instance of "App\\Entity\\RemoteCourseChapter" but this type has been excluded in "config/services.yaml".');
    }
}
