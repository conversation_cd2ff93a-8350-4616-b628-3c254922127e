<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_6fP2JZdService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.6fP2JZd' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.6fP2JZd'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'remoteCourseVideoRepository' => ['privates', 'App\\Repository\\RemoteCourseVideoRepository', 'getRemoteCourseVideoRepositoryService', true],
            'video' => ['privates', '.errored..service_locator.6fP2JZd.App\\Entity\\Video', NULL, 'Cannot autowire service ".service_locator.6fP2JZd": it needs an instance of "App\\Entity\\Video" but this type has been excluded in "config/services.yaml".'],
        ], [
            'remoteCourseVideoRepository' => 'App\\Repository\\RemoteCourseVideoRepository',
            'video' => 'App\\Entity\\Video',
        ]);
    }
}
