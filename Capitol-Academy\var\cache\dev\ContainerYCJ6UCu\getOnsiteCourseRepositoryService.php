<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getOnsiteCourseRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\OnsiteCourseRepository' shared autowired service.
     *
     * @return \App\Repository\OnsiteCourseRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'OnsiteCourseRepository.php';

        return $container->privates['App\\Repository\\OnsiteCourseRepository'] = new \App\Repository\OnsiteCourseRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
