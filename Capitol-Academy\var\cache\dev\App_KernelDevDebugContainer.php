<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerYCJ6UCu\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerYCJ6UCu/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerYCJ6UCu.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerYCJ6UCu\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerYCJ6UCu\App_KernelDevDebugContainer([
    'container.build_hash' => 'YCJ6UCu',
    'container.build_id' => 'ad9d4ab9',
    'container.build_time' => 1752924179,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerYCJ6UCu');
