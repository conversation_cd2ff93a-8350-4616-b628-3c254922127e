<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_KtXST29Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.ktXST29' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.ktXST29'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', '.errored.kmuBCOM', NULL, 'Cannot determine controller argument for "App\\Controller\\CourseController::showModule()": the $courseRepository argument is type-hinted with the non-existent class or interface: "App\\Controller\\CourseRepository". Did you forget to add a use statement?'],
            'moduleRepository' => ['privates', '.errored.IS6DTYV', NULL, 'Cannot determine controller argument for "App\\Controller\\CourseController::showModule()": the $moduleRepository argument is type-hinted with the non-existent class or interface: "App\\Controller\\CourseModuleRepository". Did you forget to add a use statement?'],
        ], [
            'courseRepository' => '?',
            'moduleRepository' => '?',
        ]);
    }
}
