<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_2o9CBriService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.2o9CBri' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.2o9CBri'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'course' => ['privates', '.errored..service_locator.2o9CBri.App\\Entity\\RemoteCourse', NULL, 'Cannot autowire service ".service_locator.2o9CBri": it needs an instance of "App\\Entity\\RemoteCourse" but this type has been excluded in "config/services.yaml".'],
        ], [
            'course' => 'App\\Entity\\RemoteCourse',
        ]);
    }
}
