<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/edit.html.twig */
class __TwigTemplate_9a97b85801d75e4a035de3db29911f67 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Onsite Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Onsite Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Edit ";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Onsite Course: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 37, $this->source); })()), "code", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0 me-2\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                        <!-- Preview Button -->
                        <a href=\"";
        // line 52
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 52, $this->source); })()), "code", [], "any", false, false, false, 52)]), "html", null, true);
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; border: 2px solid #17a2b8; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#138496';\"
                           onmouseout=\"this.style.background='#17a2b8';\">
                            <i class=\"fas fa-eye me-2\"></i>
                            Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 66
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("onsite_course_edit"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"";
        // line 67
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 67, $this->source); })()), "isActive", [], "any", false, false, false, 67)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("1") : ("0"));
        yield "\">
            <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Code <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"code\"
                                           name=\"code\"
                                           value=\"";
        // line 85
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 85, $this->source); })()), "code", [], "any", false, false, false, 85), "html", null, true);
        yield "\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Title <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"title\"
                                           name=\"title\"
                                           value=\"";
        // line 107
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 107, $this->source); })()), "title", [], "any", false, false, false, 107), "html", null, true);
        yield "\"
                                           placeholder=\"Enter course title\"
                                           required
                                           maxlength=\"255\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <!-- Category -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"category\"
                                            name=\"category\"
                                            required
                                            aria-describedby=\"category_help category_error\"
                                            aria-label=\"Select a course category\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a category...</option>
                                        ";
        // line 136
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 136, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 137
            yield "                                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 137), "html", null, true);
            yield "\" ";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 137, $this->source); })()), "category", [], "any", false, false, false, 137) == CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 137))) ? ("selected") : (""));
            yield ">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 137), "html", null, true);
            yield "</option>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 139
        yield "                                    </select>
                                    <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the category that best describes this course. Use arrow keys to navigate options.
                                    </div>
                                    <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Level <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"level\"
                                            name=\"level\"
                                            required
                                            aria-describedby=\"level_help level_error\"
                                            aria-label=\"Select course level\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a level...</option>
                                        <option value=\"Beginner\" ";
        // line 164
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 164, $this->source); })()), "level", [], "any", false, false, false, 164) == "Beginner")) ? ("selected") : (""));
        yield ">Beginner</option>
                                        <option value=\"Intermediate\" ";
        // line 165
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 165, $this->source); })()), "level", [], "any", false, false, false, 165) == "Intermediate")) ? ("selected") : (""));
        yield ">Intermediate</option>
                                        <option value=\"Advanced\" ";
        // line 166
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 166, $this->source); })()), "level", [], "any", false, false, false, 166) == "Advanced")) ? ("selected") : (""));
        yield ">Advanced</option>
                                        <option value=\"Expert\" ";
        // line 167
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 167, $this->source); })()), "level", [], "any", false, false, false, 167) == "Expert")) ? ("selected") : (""));
        yield ">Expert</option>
                                    </select>
                                    <div id=\"level_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the difficulty level of this course.
                                    </div>
                                    <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a level.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Description <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <textarea class=\"form-control enhanced-field\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Provide a detailed description of the course content, objectives, and what students will learn...\"
                                      required
                                      style=\"font-size: 1rem; border: 2px solid #ced4da; resize: vertical;\">";
        // line 191
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 191, $this->source); })()), "description", [], "any", false, false, false, 191), "html", null, true);
        yield "</textarea>
                            <div class=\"invalid-feedback\">
                                Please provide a course description.
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-bullseye text-primary mr-1\" aria-hidden=\"true\"></i>
                                Learning Outcomes <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"learning-outcomes-container\">
                                ";
        // line 204
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 204, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 204)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 205
            yield "                                    ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 205, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 205));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 206
                yield "                                        <div class=\"input-group mb-2 learning-outcome-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   value=\"";
                // line 210
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "\"
                                                   placeholder=\"Enter a learning outcome...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                ";
                // line 215
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 215)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 216
                    yield "                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                } else {
                    // line 220
                    yield "                                                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                }
                // line 227
                yield "                                            </div>
                                        </div>
                                    ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 230
            yield "                                ";
        } else {
            // line 231
            yield "                                    <div class=\"input-group mb-2 learning-outcome-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master advanced chart analysis techniques\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                ";
        }
        // line 245
        yield "                            </div>
                            <small class=\"form-text text-muted\">Add specific learning outcomes for this course. Click + to add more.</small>
                        </div>

                        <!-- Features -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-star text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Features <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"features-container\">
                                ";
        // line 256
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 256, $this->source); })()), "features", [], "any", false, false, false, 256)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 257
            yield "                                    ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 257, $this->source); })()), "features", [], "any", false, false, false, 257));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 258
                yield "                                        <div class=\"input-group mb-2 feature-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   value=\"";
                // line 262
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "\"
                                                   placeholder=\"Enter a feature...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                ";
                // line 267
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 267)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 268
                    yield "                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                } else {
                    // line 272
                    yield "                                                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                }
                // line 279
                yield "                                            </div>
                                        </div>
                                    ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 282
            yield "                                ";
        } else {
            // line 283
            yield "                                    <div class=\"input-group mb-2 feature-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Live instructor sessions, Hands-on exercises, Certificate of completion\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                ";
        }
        // line 297
        yield "                            </div>
                            <small class=\"form-text text-muted\">Add key features and benefits of this course. Click + to add more.</small>
                        </div>

                        <!-- Thumbnail Upload -->
                        <div class=\"form-group\">
                            <label for=\"thumbnail_image\" class=\"form-label\">
                                <i class=\"fas fa-image text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Thumbnail
                            </label>
                            ";
        // line 307
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 307, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 307)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 308
            yield "                                <div class=\"mb-2\">
                                    <img src=\"";
            // line 309
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 309, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 309), "html", null, true);
            yield "\" alt=\"Current thumbnail\" class=\"img-thumbnail\" style=\"max-width: 150px;\">
                                    <small class=\"text-muted d-block\">Current thumbnail</small>
                                </div>
                            ";
        }
        // line 313
        yield "                            <input type=\"file\"
                                   class=\"form-control enhanced-file-field\"
                                   id=\"thumbnail_image\"
                                   name=\"thumbnail_image\"
                                   accept=\"image/*\"
                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                            <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px
                            </small>

                            <div class=\"image-preview mt-3 d-flex flex-column align-items-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                    <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\" id=\"thumbnail-preview-img\">
                                </div>
                                <small class=\"form-text text-success d-block mt-2 text-center\">Thumbnail Preview (300x200px)</small>
                            </div>
                        </div>

                        <!-- Course has modules toggle -->
                        <div class=\"form-group\">
                            <div class=\"form-check form-switch\">
                                <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" ";
        // line 334
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 334, $this->source); })()), "hasModules", [], "any", false, false, false, 334)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("checked") : (""));
        yield ">
                                <label class=\"form-check-label\" for=\"has_modules\">
                                    <i class=\"fas fa-puzzle-piece text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Course has modules
                                </label>
                            </div>
                            <small class=\"form-text text-muted\">Enable this if you want to add modules to this course.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"card-footer\" style=\"background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;\">
                            <i class=\"fas fa-save mr-2\"></i>
                            Update Onsite Course
                        </button>
                    </div>
                    <div class=\"col-md-6 text-right\">
                        <a href=\"";
        // line 355
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\" class=\"btn btn-secondary btn-lg\" style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;\">
                            <i class=\"fas fa-times mr-2\"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 367
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 368
        yield "<script>
\$(document).ready(function() {
    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove learning outcome
    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Live instructor sessions, Downloadable resources...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove feature
    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Thumbnail image preview
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Onsite Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  722 => 368,  709 => 367,  687 => 355,  663 => 334,  640 => 313,  633 => 309,  630 => 308,  628 => 307,  616 => 297,  600 => 283,  597 => 282,  581 => 279,  572 => 272,  566 => 268,  564 => 267,  556 => 262,  550 => 258,  532 => 257,  530 => 256,  517 => 245,  501 => 231,  498 => 230,  482 => 227,  473 => 220,  467 => 216,  465 => 215,  457 => 210,  451 => 206,  433 => 205,  431 => 204,  415 => 191,  388 => 167,  384 => 166,  380 => 165,  376 => 164,  349 => 139,  336 => 137,  332 => 136,  300 => 107,  275 => 85,  254 => 67,  250 => 66,  233 => 52,  221 => 43,  212 => 37,  202 => 29,  192 => 25,  189 => 24,  185 => 23,  182 => 22,  172 => 18,  169 => 17,  165 => 16,  161 => 14,  148 => 13,  135 => 10,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Onsite Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Onsite Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Edit {{ course.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Onsite Course: {{ course.code }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0 me-2\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                        <!-- Preview Button -->
                        <a href=\"{{ path('admin_onsite_course_preview', {'code': course.code}) }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; border: 2px solid #17a2b8; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#138496';\"
                           onmouseout=\"this.style.background='#17a2b8';\">
                            <i class=\"fas fa-eye me-2\"></i>
                            Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('onsite_course_edit') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"{{ course.isActive ? '1' : '0' }}\">
            <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Code <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"code\"
                                           name=\"code\"
                                           value=\"{{ course.code }}\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Title <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"title\"
                                           name=\"title\"
                                           value=\"{{ course.title }}\"
                                           placeholder=\"Enter course title\"
                                           required
                                           maxlength=\"255\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <!-- Category -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"category\"
                                            name=\"category\"
                                            required
                                            aria-describedby=\"category_help category_error\"
                                            aria-label=\"Select a course category\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a category...</option>
                                        {% for category in categories %}
                                            <option value=\"{{ category.name }}\" {{ course.category == category.name ? 'selected' : '' }}>{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the category that best describes this course. Use arrow keys to navigate options.
                                    </div>
                                    <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Level <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"level\"
                                            name=\"level\"
                                            required
                                            aria-describedby=\"level_help level_error\"
                                            aria-label=\"Select course level\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a level...</option>
                                        <option value=\"Beginner\" {{ course.level == 'Beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value=\"Intermediate\" {{ course.level == 'Intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value=\"Advanced\" {{ course.level == 'Advanced' ? 'selected' : '' }}>Advanced</option>
                                        <option value=\"Expert\" {{ course.level == 'Expert' ? 'selected' : '' }}>Expert</option>
                                    </select>
                                    <div id=\"level_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the difficulty level of this course.
                                    </div>
                                    <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a level.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Description <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <textarea class=\"form-control enhanced-field\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Provide a detailed description of the course content, objectives, and what students will learn...\"
                                      required
                                      style=\"font-size: 1rem; border: 2px solid #ced4da; resize: vertical;\">{{ course.description }}</textarea>
                            <div class=\"invalid-feedback\">
                                Please provide a course description.
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-bullseye text-primary mr-1\" aria-hidden=\"true\"></i>
                                Learning Outcomes <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"learning-outcomes-container\">
                                {% if course.learningOutcomes %}
                                    {% for outcome in course.learningOutcomes %}
                                        <div class=\"input-group mb-2 learning-outcome-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   value=\"{{ outcome }}\"
                                                   placeholder=\"Enter a learning outcome...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                {% if loop.first %}
                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% else %}
                                                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class=\"input-group mb-2 learning-outcome-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master advanced chart analysis techniques\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            <small class=\"form-text text-muted\">Add specific learning outcomes for this course. Click + to add more.</small>
                        </div>

                        <!-- Features -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-star text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Features <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"features-container\">
                                {% if course.features %}
                                    {% for feature in course.features %}
                                        <div class=\"input-group mb-2 feature-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   value=\"{{ feature }}\"
                                                   placeholder=\"Enter a feature...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                {% if loop.first %}
                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% else %}
                                                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class=\"input-group mb-2 feature-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Live instructor sessions, Hands-on exercises, Certificate of completion\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            <small class=\"form-text text-muted\">Add key features and benefits of this course. Click + to add more.</small>
                        </div>

                        <!-- Thumbnail Upload -->
                        <div class=\"form-group\">
                            <label for=\"thumbnail_image\" class=\"form-label\">
                                <i class=\"fas fa-image text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Thumbnail
                            </label>
                            {% if course.thumbnailImage %}
                                <div class=\"mb-2\">
                                    <img src=\"{{ course.thumbnailUrl }}\" alt=\"Current thumbnail\" class=\"img-thumbnail\" style=\"max-width: 150px;\">
                                    <small class=\"text-muted d-block\">Current thumbnail</small>
                                </div>
                            {% endif %}
                            <input type=\"file\"
                                   class=\"form-control enhanced-file-field\"
                                   id=\"thumbnail_image\"
                                   name=\"thumbnail_image\"
                                   accept=\"image/*\"
                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                            <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px
                            </small>

                            <div class=\"image-preview mt-3 d-flex flex-column align-items-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                    <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\" id=\"thumbnail-preview-img\">
                                </div>
                                <small class=\"form-text text-success d-block mt-2 text-center\">Thumbnail Preview (300x200px)</small>
                            </div>
                        </div>

                        <!-- Course has modules toggle -->
                        <div class=\"form-group\">
                            <div class=\"form-check form-switch\">
                                <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" {{ course.hasModules ? 'checked' : '' }}>
                                <label class=\"form-check-label\" for=\"has_modules\">
                                    <i class=\"fas fa-puzzle-piece text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Course has modules
                                </label>
                            </div>
                            <small class=\"form-text text-muted\">Enable this if you want to add modules to this course.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"card-footer\" style=\"background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;\">
                            <i class=\"fas fa-save mr-2\"></i>
                            Update Onsite Course
                        </button>
                    </div>
                    <div class=\"col-md-6 text-right\">
                        <a href=\"{{ path('admin_onsite_courses') }}\" class=\"btn btn-secondary btn-lg\" style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;\">
                            <i class=\"fas fa-times mr-2\"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove learning outcome
    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Live instructor sessions, Downloadable resources...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove feature
    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Thumbnail image preview
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Onsite Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
{% endblock %}
", "admin/onsite_courses/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\edit.html.twig");
    }
}
