<?php

namespace ContainerYCJ6UCu;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getRemoteCourseService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.2o9CBri.App\Entity\RemoteCourse' shared service.
     *
     * @return \App\Entity\RemoteCourse
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.2o9CBri": it needs an instance of "App\\Entity\\RemoteCourse" but this type has been excluded in "config/services.yaml".');
    }
}
